2025-07-13 12:11:09,261 - discord.client - WARNING - PyNaCl is not installed, voice will NOT be supported
2025-07-13 12:11:09,277 - discord.client - INFO - logging in using static token
2025-07-13 12:11:10,513 - discord.gateway - INFO - Shard ID None has connected to Gateway (Session ID: 8b7eeb370d3639ad3d7be7b5682f3236).
2025-07-20 08:30:56,352 - discord.client - WARNING - PyNaCl is not installed, voice will NOT be supported
2025-07-20 08:30:56,367 - discord.client - INFO - logging in using static token
2025-07-20 08:30:58,072 - discord.gateway - INFO - Shard ID None has connected to Gateway (Session ID: 67bb97d2f11df634bbb8ee36b9c53035).
2025-07-20 08:33:02,102 - discord.client - WARNING - PyNaCl is not installed, voice will NOT be supported
2025-07-20 08:33:02,116 - discord.client - INFO - logging in using static token
2025-07-20 08:33:04,626 - discord.gateway - INFO - Shard ID None has connected to Gateway (Session ID: 183869714ecc770cc19cf79417faf1ee).
2025-07-20 08:37:32,514 - discord.app_commands.tree - ERROR - Ignoring exception in autocomplete for 'remove_item'
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\discord\app_commands\tree.py", line 1302, in _call
    await command._invoke_autocomplete(interaction, focused, namespace)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\discord\app_commands\commands.py", line 927, in _invoke_autocomplete
    await interaction.response.autocomplete(choices)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\discord\interactions.py", line 1279, in autocomplete
    await adapter.create_interaction_response(
    ...<6 lines>...
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\discord\webhook\async_.py", line 226, in request
    raise HTTPException(response, data)
discord.errors.HTTPException: 400 Bad Request (error code: 40060): Interaction has already been acknowledged.
2025-07-20 08:37:34,542 - BankBot - ERROR - Command error: remove_item by 207295469133496322 (.madwezza) in 1057306241354960937: Command 'remove_item' raised an exception: NotFound: 404 Not Found (error code: 10062): Unknown interaction
2025-07-20 08:37:34,996 - BankBot - CRITICAL - Error in error handler: 400 Bad Request (error code: 40060): Interaction has already been acknowledged. (Original error: Command 'remove_item' raised an exception: NotFound: 404 Not Found (error code: 10062): Unknown interaction)
2025-07-20 08:38:13,870 - BankBot - ERROR - Command error: destroy by 207295469133496322 (.madwezza) in 1057306241354960937: Command 'destroy' raised an exception: HTTPException: 400 Bad Request (error code: 40060): Interaction has already been acknowledged.
2025-07-20 08:38:14,060 - BankBot - CRITICAL - Error in error handler: 400 Bad Request (error code: 40060): Interaction has already been acknowledged. (Original error: Command 'destroy' raised an exception: HTTPException: 400 Bad Request (error code: 40060): Interaction has already been acknowledged.)
